<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use App\Models\User;
use App\Models\ItemColor;
use App\Models\OrderPrice;
use App\Models\UserItemColorPrice;
use App\Models\OrderHistory;
use App\Models\PriceLevelList;
use App\Models\Category;
use Illuminate\Support\Str;
use PDF;
use DB;
use Illuminate\Support\Facades\Mail;
use Storage;
use Illuminate\Support\Facades\Validator;
use App\Http\Controllers\EmailController;
use App\Events\OrderCreated;
use App\Events\OrderStatusChanged;
use App\Events\OrderAssignedToSalesman;

class OrderController extends Controller
{
    public function index()
    {
        $orders=Order::orderBy('id','DESC')->paginate(10);
        return view('backend.orders.index')->with('orders',$orders);
    }

    public function show($id)
    {
        $order=Order::find($id);
        return view('backend.orders.show')->with('order',$order);
    }

    public function generate_pdf($id)
    {
        $order=Order::getAllOrder($id);
        $file_name=$order->order_number.'-'.$order->first_name.'.pdf';
        $pdf=PDF::loadview('backend.orders.pdf',compact('order'));
        return $pdf->download($file_name);
    }

    public function create()
    {
        $price_level_lists = PriceLevelList::all();
        $categories = Category::where('type', 'user')->get();
        $products = Product::with('item_colors')->where('status', 'active')->get();
        $salesmen = User::where('role', 'salesman')->where('status', 'active')->get();
        return view('backend.orders.create', compact('products','salesmen', 'price_level_lists', 'categories'));
    }

    public function store(Request $request)
    {
        try {
            DB::beginTransaction();

            $validated = $request->validate([
                'salesman_id' => 'required|exists:users,id',
                'user_id' => 'required|exists:users,id',
                'delivery_method' => 'required|string|in:pickup,ship,delivery,instant',
                'signature_data' => 'required_if:delivery_method,instant|string|nullable',
                'is_paid' => 'required_if:delivery_method,instant|boolean',
                'items' => 'required|array|min:1',
                'items.*.product_id' => 'required|exists:products,id',
                'items.*.color' => 'required|exists:colors,id',
                'items.*.price' => 'required|numeric|min:0',
                'items.*.quantity' => 'required|integer|min:1',
                'items.*.is_one_time' => 'nullable|in:on,0,1',
                'items.*.use_custom_price' => 'nullable|in:on,0,1',
                'shipping_price' => 'nullable|numeric|min:0',
                'safe_for_future' => 'nullable|boolean',
            ]);

            $customer = User::findOrFail($validated['user_id']);
            $salesman = User::findOrFail($validated['salesman_id']);

            $address1 = $customer->shipping_address ?? $customer->billing_address;
            $address2 = $customer->shipping_city ?? $customer->billing_city;
            $post_code = $customer->shipping_zip ?? $customer->billing_zip;
            $state = $customer->shipping_state ?? $customer->billing_state;

            $subTotal = 0;
            $totalQty = 0;
            foreach ($validated['items'] as $index => $item) {
                $itemColor = ItemColor::where('product_id', $item['product_id'])
                    ->where('color_id', $item['color'])->first();

                if (!$itemColor || $item['quantity'] > $itemColor->stock) {
                    DB::rollBack();
                    return back()->withErrors([
                        "items.$index.quantity" => "Only " . ($itemColor ? $itemColor->stock : 0) . " left in stock for " . ($itemColor ? $itemColor->item_number : 'item') . " (" . ($itemColor ? $itemColor->color->name : 'color') . ")."
                    ])->withInput();
                }

                $subTotal += $item['price'] * $item['quantity'];
                $totalQty += $item['quantity'];
            }

            $shippingPrice = $validated['shipping_price'] ?? 0;
            $totalAmount = $subTotal + $shippingPrice;

            $order = Order::create([
                'order_number' => 'ORD-' . strtoupper(Str::random(10)),
                'user_id' => $customer->id,
                'salesman_id' => $salesman->id,
                'delivery_method' => $validated['delivery_method'],
                'safe_for_future' => $request->has('safe_for_future'),
                'is_instant_order' => $validated['delivery_method'] === 'instant',
                'is_paid' => $validated['delivery_method'] === 'instant' ? $request->has('is_paid') : false,
                'sub_total' => $subTotal,
                'shipping_price' => $shippingPrice,
                'total_amount' => $totalAmount,
                'amount_left' => $totalAmount,
                'quantity' => $totalQty,
                'first_name' => $customer->first_name ?? 'N/A',
                'last_name' => $customer->last_name ?? 'N/A',
                'email' => $customer->email ?? 'N/A',
                'phone' => $customer->contact_phone ?? $customer->account_phone ?? 'N/A',
                'country' => 'NP',
                'post_code' => $post_code,
                'address1' => $address1,
                'address2' => $state,
                'status' => 'pending',
            ]);

            $this->logOrderHistory($order, auth()->id(), $customer->id, 'created_order', [
                'order_number' => $order->order_number,
                'user_id' => $customer->id,
                'salesman_id' => $salesman->id,
                'delivery_method' => $validated['delivery_method'],
                'total_amount' => $totalAmount,
            ]);

            foreach ($validated['items'] as $item) {
                $product = Product::find($item['product_id']);
                $itemColor = ItemColor::where('product_id', $item['product_id'])
                    ->where('color_id', $item['color'])->first();

                $prices = $itemColor ? $itemColor->getPriceForUserAttribute($customer->id) : [
                    'original_price' => $item['price'],
                    'custom_price' => $item['price'],
                ];

                $useCustomPrice = isset($item['use_custom_price']) && in_array($item['use_custom_price'], ['on', '1', 1, true]);
                $isOneTime = $item['is_one_time'] ?? '1';
                $itemPrice = $item['price'] ?? 0;
                $originalPrice = $prices['original_price'] ?? 0;
                $systemCustomPrice = $prices['custom_price'] ?? 0;

                // If using custom price, use the entered price, otherwise use the system's custom price
                $priceToUse = $useCustomPrice ? $itemPrice : $systemCustomPrice;

                // Save custom price for future if checkbox is checked and is_one_time is '0'
                if ($useCustomPrice && $isOneTime === '0') {
                    UserItemColorPrice::updateOrCreate(
                        [
                            'user_id' => $customer->id,
                            'item_color_id' => $itemColor->id
                        ],
                        [
                            'custom_price' => $itemPrice
                        ]
                    );
                }

                // Create OrderItem record
                $order->items()->create([
                    'product_id' => $product->id,
                    'product_name' => $product->title,
                    'price' => $priceToUse,
                    'original_price' => $prices['original_price'] ?? 0,
                    'quantity' => $item['quantity'],
                    'total' => $priceToUse * $item['quantity'],
                    'options' => json_encode([]),
                    'color' => $item['color'],
                ]);

                // Also create Cart record for picker system compatibility
                \App\Models\Cart::create([
                    'user_id' => $customer->id,
                    'product_id' => $product->id,
                    'order_id' => $order->id,
                    'quantity' => $item['quantity'],
                    'price' => $priceToUse,
                    'amount' => $priceToUse * $item['quantity'],
                    'color' => $item['color'],
                    'status' => 'new'
                ]);

                $this->logOrderHistory($order, auth()->id(), $customer->id, 'added_item', [
                    'product_id' => $item['product_id'],
                    'color' => $item['color'],
                    'quantity' => $item['quantity'],
                    'price' => $priceToUse,
                ]);

                $itemColor->decrement('stock', $item['quantity']);
            }

            if ($request->filled('signature_data')) {
                $base64 = $request->input('signature_data');
                $image = str_replace('data:image/png;base64,', '', $base64);
                $image = str_replace(' ', '+', $image);
                $imageName = 'signature_' . time() . '.png';

                Storage::disk('public')->put("signatures/{$imageName}", base64_decode($image));

                $order->signature_data = "signatures/{$imageName}";
                $order->save();

                $this->logOrderHistory($order, auth()->id(), $customer->id, 'added_signature', [
                    'signature_path' => $order->signature_data,
                ]);
            }

            $pdf = PDF::loadView('backend.orders.packing-slip', compact('order'));
            $pdfPath = 'packing_slips/' . $order->order_number . '.pdf';
            Storage::disk('public')->put($pdfPath, $pdf->output());
            $order->packing_slip_url = $pdfPath;
            $order->save();

            DB::commit();

            // Dispatch order created event for email notifications
            event(new OrderCreated($order));

            return redirect()->route('orders.create')->with([
                'success' => 'Order created successfully.',
                'data' => $order->toArray()
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->route('orders.create')->with('error', 'Error creating order: ' . $e->getMessage())->withInput();
        }
    }

    public function edit($id)
    {
        $order = Order::with('items')->findOrFail($id);
        $products = Product::where('status', 'active')->get();
        $salesmen = User::where('role', 'salesman')->where('status', 'active')->get();
        $pickers = User::where('role', 'picker')->where('status', 'active')->get();
        $previous_orders = Order::where('user_id', $order->user_id)->where('id', '!=', $id)->latest()->get();
        return view('backend.orders.edit', compact('order', 'products', 'salesmen', 'previous_orders','pickers'));
    }

    public function assignToPicker(Request $request)
    {
        try {
            $request->validate([
                'order_id' => 'required|exists:orders,id',
                'picker_id' => 'required|exists:users,id'
            ]);

            $order = Order::findOrFail($request->order_id);
            $picker = User::where('id', $request->picker_id)
                         ->where('role', 'picker')
                         ->where('status', 'active')
                         ->firstOrFail();

            // Update order with picker assignment
            $order->update([
                'picker_id' => $picker->id,
                'status' => 'sent_to_warehouse'
            ]);

            // Try to log the assignment (with error handling)
            try {
                $this->logOrderHistory($order, auth()->id(), $order->user_id, 'assigned_to_picker', [
                    'picker_id' => $picker->id,
                    'picker_name' => $picker->first_name . ' ' . $picker->last_name,
                    'assigned_by' => auth()->user()->first_name . ' ' . auth()->user()->last_name,
                ]);
            } catch (\Exception $logError) {
                // Log error but don't fail the assignment
                \Log::error('Failed to log order history: ' . $logError->getMessage());
            }

            // Try to send notification email (with error handling)
            try {
                $customerData = [
                    'order' => $order,
                    'picker' => $picker,
                    'assigned_by' => auth()->user()
                ];

                // Mail::to($picker->email, $picker->first_name . ' ' . $picker->last_name)
                //     ->queue(new \App\Mail\MailQueue(
                //         'backend.emails.order_picker_assignment',
                //         $customerData,
                //         'New Order Assignment - #' . $order->order_number
                //     ));
            } catch (\Exception $mailError) {
                // Log error but don't fail the assignment
                \Log::error('Failed to send assignment email: ' . $mailError->getMessage());
            }

            return response()->json([
                'status' => true,
                'message' => 'Order successfully assigned to ' . $picker->first_name . ' ' . $picker->last_name
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to assign order: ' . $e->getMessage()
            ]);
        }
    }

    public function bulkAssignToPicker(Request $request)
    {
        try {
            $request->validate([
                'order_ids' => 'required|array',
                'order_ids.*' => 'exists:orders,id',
                'picker_id' => 'required|exists:users,id'
            ]);

            $picker = User::where('id', $request->picker_id)
                         ->where('role', 'picker')
                         ->where('status', 'active')
                         ->firstOrFail();

            $orders = Order::whereIn('id', $request->order_ids)->get();
            $assignedCount = 0;

            foreach ($orders as $order) {
                if (!$order->picker_id) { // Only assign if not already assigned
                    $order->update([
                        'picker_id' => $picker->id,
                        'status' => 'sent_to_warehouse'
                    ]);

                    // Try to log the assignment (with error handling)
                    try {
                        $this->logOrderHistory($order, auth()->id(), $order->user_id, 'assigned_to_picker', [
                            'picker_id' => $picker->id,
                            'picker_name' => $picker->first_name . ' ' . $picker->last_name,
                            'assigned_by' => auth()->user()->first_name . ' ' . auth()->user()->last_name,
                        ]);
                    } catch (\Exception $logError) {
                        \Log::error('Failed to log order history for bulk assignment: ' . $logError->getMessage());
                    }

                    $assignedCount++;
                }
            }

            // Send bulk notification email to picker
            if ($assignedCount > 0) {
                $customerData = [
                    'orders' => $orders->where('picker_id', $picker->id),
                    'picker' => $picker,
                    'assigned_by' => auth()->user(),
                    'count' => $assignedCount
                ];

                Mail::to($picker->email, $picker->first_name . ' ' . $picker->last_name)
                    ->queue(new \App\Mail\MailQueue(
                        'backend.emails.bulk_order_picker_assignment',
                        $customerData,
                        $assignedCount . ' Orders Assigned to You'
                    ));
            }

            return response()->json([
                'status' => true,
                'message' => $assignedCount . ' orders successfully assigned to ' . $picker->first_name . ' ' . $picker->last_name
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to assign orders: ' . $e->getMessage()
            ]);
        }
    }

    public function update(Request $request, $id)
    {
        try {
            DB::beginTransaction();

            $validated = $request->validate([
                'salesman_id' => 'required|exists:users,id',
                'user_id' => 'required|exists:users,id',
                'delivery_method' => 'required|string|in:pickup,ship,delivery,instant',
                'signature_data' => 'required_if:delivery_method,instant|string|nullable',
                'is_paid' => 'required_if:delivery_method,instant|boolean',
                'items' => 'required|array|min:1',
                'items.*.product_id' => 'required|exists:products,id',
                'items.*.quantity' => 'required|integer|min:1',
                'items.*.price' => 'required|numeric|min:0',
                'items.*.original_price' => 'required|numeric|min:0',
                'items.*.color' => 'required|exists:colors,id',
                'items.*.use_custom_price' => 'nullable|in:on,0,1',
                'items.*.is_one_time' => 'nullable|in:on,0,1',
                'shipping_price' => 'nullable|numeric|min:0',
                'safe_for_future' => 'nullable|boolean',
            ]);

            $order = Order::with('items')->findOrFail($id);
            $customer = User::findOrFail($validated['user_id']);
            $salesman = User::findOrFail($validated['salesman_id']);

            $this->logOrderHistory($order, auth()->id(), $customer->id, 'edited_order', [
                'original' => $order->toArray(),
                'items' => $order->items->toArray(),
            ]);

            $address1 = $customer->shipping_address ?? $customer->billing_address;
            $address2 = $customer->shipping_city ?? $customer->billing_city;
            $post_code = $customer->shipping_zip ?? $customer->billing_zip;
            $state = $customer->shipping_state ?? $customer->billing_state;

            $subTotal = 0;
            $totalQty = 0;
            foreach ($validated['items'] as $item) {
                $subTotal += $item['price'] * $item['quantity'];
                $totalQty += $item['quantity'];
            }
            $shippingPrice = $validated['shipping_price'] ?? 0;
            $totalAmount = $subTotal + $shippingPrice;
            if(!empty($request->picker_id)){
                $status = 'sent_to_warehouse';
            }

            $order->update([
                'user_id' => $customer->id,
                'salesman_id' => $salesman->id,
                'delivery_method' => $validated['delivery_method'],
                'safe_for_future' => $request->has('safe_for_future'),
                'is_instant_order' => $validated['delivery_method'] === 'instant',
                'is_paid' => $request->has('is_paid'),
                'sub_total' => $subTotal,
                'shipping_price' => $shippingPrice,
                'total_amount' => $totalAmount,
                'amount_left' => $totalAmount,
                'quantity' => $totalQty,
                'first_name' => $customer->first_name ?? 'N/A',
                'last_name' => $customer->last_name ?? 'N/A',
                'email' => $customer->email ?? 'N/A',
                'phone' => $customer->contact_phone ?? $customer->account_phone ?? 'N/A',
                'country' => 'NP',
                'post_code' => $post_code,
                'address1' => $address1,
                'address2' => $state,
                'picker_id' => $request->picker_id ?? null,
                'status' => !empty($request->picker_id) ? 'sent_to_warehouse' : $order->status,
            ]);

            $existingItems = $order->items->keyBy(function ($item) {
                return $item->product_id . '_' . $item->color;
            })->map(function ($item) {
                return [
                    'quantity' => $item->quantity,
                    'price' => $item->price,
                ];
            })->toArray();

            foreach ($order->items as $existingItem) {
                $itemColor = ItemColor::where('product_id', $existingItem->product_id)
                    ->where('color_id', $existingItem->color)
                    ->first();
                if ($itemColor) {
                    $itemColor->increment('stock', $existingItem->quantity);
                }
            }

            $order->items()->delete();

            foreach ($validated['items'] as $index => $item) {
                $itemColor = ItemColor::where('product_id', $item['product_id'])
                    ->where('color_id', $item['color'])
                    ->first();
                if (!$itemColor || $itemColor->stock < $item['quantity']) {
                    DB::rollBack();
                    return back()->withErrors([
                        "items.$index.quantity" => "Only {$itemColor->stock} left in stock for {$itemColor->item_number}."
                    ])->withInput();
                }

                $prices = $itemColor->getPriceForUserAttribute($customer->id);
                $originalPrice = $prices['original_price'];
                $systemCustomPrice = $prices['custom_price'];
                $itemKey = $item['product_id'] . '_' . $item['color'];

                $useCustomPrice = isset($item['use_custom_price']) && in_array($item['use_custom_price'], ['on', '1', 1, true]);

                // Determine the price to use
                $priceToUse = $useCustomPrice ? $item['price'] : $systemCustomPrice;

                if ($useCustomPrice && $item['is_one_time'] == '0') {
                    UserItemColorPrice::updateOrCreate(
                        [
                            'user_id' => $customer->id,
                            'item_color_id' => $itemColor->id,
                        ],
                        [
                            'custom_price' => $item['price'],
                        ]
                    );
                } elseif ($item['is_one_time'] == '1') {
                    UserItemColorPrice::where('user_id', $customer->id)
                        ->where('item_color_id', $itemColor->id)
                        ->delete();
                }

                $order->items()->create([
                    'product_id' => $item['product_id'],
                    'product_name' => $itemColor->product->title,
                    'price' => $priceToUse,
                    'original_price' => $prices['original_price'],
                    'quantity' => $item['quantity'],
                    'total' => $priceToUse * $item['quantity'],
                    'options' => json_encode([]),
                    'color' => $item['color'],
                ]);

                if (isset($existingItems[$itemKey])) {
                    if ($existingItems[$itemKey]['quantity'] != $item['quantity'] || $existingItems[$itemKey]['price'] != $item['price']) {
                        $this->logOrderHistory($order, auth()->id(), $customer->id, 'updated_item', [
                            'product_id' => $item['product_id'],
                            'color' => $item['color'],
                            'old_quantity' => $existingItems[$itemKey]['quantity'],
                            'new_quantity' => $item['quantity'],
                            'old_price' => $existingItems[$itemKey]['price'],
                            'new_price' => $item['price'],
                        ]);
                    }
                    unset($existingItems[$itemKey]);
                } else {
                    $this->logOrderHistory($order, auth()->id(), $customer->id, 'added_item', [
                        'product_id' => $item['product_id'],
                        'color' => $item['color'],
                        'quantity' => $item['quantity'],
                        'price' => $item['price'],
                    ]);
                }

                $itemColor->decrement('stock', $item['quantity']);
            }

            foreach ($existingItems as $itemKey => $itemData) {
                [$productId, $colorId] = explode('_', $itemKey);
                $this->logOrderHistory($order, auth()->id(), $customer->id, 'removed_item', [
                    'product_id' => $productId,
                    'color' => $colorId,
                    'quantity' => $itemData['quantity'],
                    'price' => $itemData['price'],
                ]);
            }

            // Update signature
            if ($request->filled('signature_data')) {
                if ($order->signature_data && Storage::disk('public')->exists($order->signature_data)) {
                    Storage::disk('public')->delete($order->signature_data);
                }
                $base64 = $request->input('signature_data');
                $image = str_replace('data:image/png;base64,', '', $base64);
                $image = str_replace(' ', '+', $image);
                $imageName = 'signature_' . time() . '.png';
                Storage::disk('public')->put("signatures/{$imageName}", base64_decode($image));
                $order->signature_data = "signatures/{$imageName}";
                $order->save();

                $this->logOrderHistory($order, auth()->id(), $customer->id, 'updated_signature', [
                    'signature_path' => $order->signature_data,
                ]);
            } elseif ($order->signature_data) {
                if (Storage::disk('public')->exists($order->signature_data)) {
                    Storage::disk('public')->delete($order->signature_data);
                }
                $order->signature_data = null;
                $order->save();

                $this->logOrderHistory($order, auth()->id(), $customer->id, 'removed_signature', []);
            }

            // Regenerate packing slip PDF
            $pdf = PDF::loadView('backend.orders.packing-slip', compact('order'));
            $pdfPath = 'packing_slips/' . $order->order_number . '.pdf';
            Storage::disk('public')->put($pdfPath, $pdf->output());
            $order->packing_slip_url = $pdfPath;
            $order->save();

            // Email PDF to customer (commented out as in store)
            // Mail::send('backend.emails.order-confirmation', compact('order'), function ($message) use ($order, $pdfPath) {
            //     $message->to($order->email)
            //             ->subject('Your Order Confirmation')
            //             ->attach(storage_path("app/public/{$pdfPath}"));
            // });

            DB::commit();
            return redirect()->route('orders.index')->with('success', 'Order updated successfully.');
        } catch (\Exception $e) {
            DB::rollBack();
            dd($e);
            return redirect()->back()->with('error', 'Error updating order: ' . $e->getMessage())->withInput();
        }
    }

    private function logOrderHistory(Order $order, int $user_id, int $customer_id, string $action, array $details)
    {
        OrderHistory::create([
            'order_id' => $order->id,
            'user_id' => $user_id,
            'customer_id' => $customer_id,
            'action' => $action,
            'details' => $details,
        ]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $delete=Order::findorFail($id);
        $status=$delete->delete();
        if($status){
            request()->session()->flash('success','User Successfully deleted');
        }
        else{
            request()->session()->flash('error','There is an error while deleting users');
        }
        return redirect()->route('orders.index');
    }

    public function checkStock(Request $request)
    {
        $validated = $request->validate([
            'items' => 'required|array',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.color' => 'required|exists:colors,id',
            'items.*.quantity' => 'required|integer|min:1',
        ]);

        $errors = [];
        foreach ($validated['items'] as $index => $item) {
            $itemColor = ItemColor::where('product_id', $item['product_id'])
                ->where('color_id', $item['color'])->first();
            if (!$itemColor || $item['quantity'] > $itemColor->stock) {
                $errors[] = [
                    'index' => $index,
                    'product' => $itemColor ? "{$itemColor->item_number} ({$itemColor->color->name})" : 'Unknown Product',
                    'available' => $itemColor ? $itemColor->stock : 0,
                    'requested' => $item['quantity'],
                ];
            }
        }

        if (!empty($errors)) {
            return response()->json(['errors' => $errors], 422);
        }

        return response()->json(['message' => 'Stock available'], 200);
    }

    public function getProductPrices(Request $request)
    {
        $userId = $request->query('user_id');
        $products = Product::with(['item_colors.color'])->get()->map(function ($product) use ($userId) {
            $product->item_colors->each(function ($itemColor) use ($userId) {
                $itemColor->prices = $itemColor->getPriceForUserAttribute($userId);
            });
            return $product;
        });

        return response()->json($products);
    }

    public function sendOrderDetails($id)
    {
        try {
            $order = Order::with(['items.product', 'shipping'])->findOrFail($id);
            $email_controller = new EmailController();
            $response = $email_controller->sendOrderDetails($order);

            return $response;
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to send order details: ' . $e->getMessage()
            ], 500);
        }
    }
}
