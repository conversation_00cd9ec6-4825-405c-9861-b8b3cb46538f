<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('carts', function (Blueprint $table) {
            if (!Schema::hasColumn('carts', 'original_cart_id')) {
                $table->unsignedBigInteger('original_cart_id')->nullable()->after('box_id');
                $table->index('original_cart_id');
            }
            if (!Schema::hasColumn('carts', 'packed_at')) {
                $table->timestamp('packed_at')->nullable()->after('backorder_quantity');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('carts', function (Blueprint $table) {
            if (Schema::hasColumn('carts', 'original_cart_id')) {
                $table->dropIndex(['original_cart_id']);
                $table->dropColumn('original_cart_id');
            }
            if (Schema::hasColumn('carts', 'packed_at')) {
                $table->dropColumn('packed_at');
            }
        });
    }
};
