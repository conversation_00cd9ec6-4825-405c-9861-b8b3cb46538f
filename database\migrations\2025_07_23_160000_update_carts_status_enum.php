<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update the status enum to include 'distributed'
        DB::statement("ALTER TABLE carts MODIFY COLUMN status ENUM('new','progress','delivered','cancel','distributed') DEFAULT 'new'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert back to original enum values
        DB::statement("ALTER TABLE carts MODIFY COLUMN status ENUM('new','progress','delivered','cancel') DEFAULT 'new'");
    }
};
